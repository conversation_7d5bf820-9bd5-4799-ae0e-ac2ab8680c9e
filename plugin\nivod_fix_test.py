# -*- coding: utf-8 -*-
# nivod.vip 插件修复验证测试脚本

import sys
sys.path.append('..')
import json
from nivod import Spider

def test_search_fix():
    """测试搜索功能修复"""
    print("=" * 60)
    print("测试搜索功能修复")
    print("=" * 60)
    
    spider = Spider()
    spider.init()
    
    # 测试搜索关键词
    keywords = ['利剑', '山羊', '哪吒']
    
    for keyword in keywords:
        print(f"\n搜索关键词: '{keyword}'")
        result = spider.searchContent(keyword, False, '1')
        
        print(f"搜索结果数量: {len(result.get('list', []))}")
        
        if result.get('list'):
            print("搜索结果:")
            for i, video in enumerate(result['list'][:3]):
                print(f"  {i+1}. {video.get('vod_name', '')} (ID: {video.get('vod_id', '')})")
                print(f"     图片: {video.get('vod_pic', '')}")
                print(f"     备注: {video.get('vod_remarks', '')}")
        else:
            print("  无搜索结果")

def test_category_fix():
    """测试分类功能修复"""
    print("\n" + "=" * 60)
    print("测试分类功能修复")
    print("=" * 60)
    
    spider = Spider()
    spider.init()
    
    # 测试电影分类
    print("测试电影分类:")
    result = spider.categoryContent('1', '1', False, {})
    
    print(f"找到视频数量: {len(result.get('list', []))}")
    
    if result.get('list'):
        print("\n前5个视频:")
        for i, video in enumerate(result['list'][:5]):
            print(f"  {i+1}. {video.get('vod_name', '')} (ID: {video.get('vod_id', '')})")
            print(f"     图片: {video.get('vod_pic', '')}")
            print(f"     备注: {video.get('vod_remarks', '')}")

def test_detail_fix():
    """测试详情功能修复"""
    print("\n" + "=" * 60)
    print("测试详情功能修复")
    print("=" * 60)
    
    spider = Spider()
    spider.init()
    
    # 测试具体视频的详情
    test_ids = ['81251', '84548']  # 山羊生活, 利剑·玫瑰
    
    for vod_id in test_ids:
        print(f"\n测试视频ID: {vod_id}")
        result = spider.detailContent([vod_id])
        
        if result.get('list'):
            detail = result['list'][0]
            print(f"  标题: {detail.get('vod_name', '')}")
            print(f"  年份: {detail.get('vod_year', '')}")
            print(f"  地区: {detail.get('vod_area', '')}")
            print(f"  类型: {detail.get('type_name', '')}")
            print(f"  导演: {detail.get('vod_director', '')}")
            print(f"  主演: {detail.get('vod_actor', '')}")
            print(f"  图片: {detail.get('vod_pic', '')}")
            
            # 播放源信息
            play_from = detail.get('vod_play_from', '')
            play_url = detail.get('vod_play_url', '')
            
            if play_from:
                sources = play_from.split('$$$')
                print(f"  播放源数量: {len(sources)}")
                print(f"  播放源: {', '.join(sources)}")
            
            if play_url:
                urls = play_url.split('$$$')
                for i, url_list in enumerate(urls):
                    episodes = url_list.split('#')
                    print(f"  播放源{i+1}集数: {len(episodes)}")
                    if episodes:
                        print(f"  第一集: {episodes[0]}")
            
            # 剧情简介
            content = detail.get('vod_content', '')
            if content:
                print(f"  剧情: {content[:100]}...")
        else:
            print(f"  获取详情失败")

def test_player_fix():
    """测试播放地址修复"""
    print("\n" + "=" * 60)
    print("测试播放地址修复")
    print("=" * 60)
    
    spider = Spider()
    spider.init()
    
    # 测试播放地址
    test_urls = [
        '/niplay/81251-1-1/',  # 山羊生活
        '/niplay/84548-1-1/',  # 利剑·玫瑰
    ]
    
    for play_url in test_urls:
        print(f"\n测试播放URL: {play_url}")
        result = spider.playerContent('', play_url, '')
        
        print(f"解析结果:")
        print(f"  需要解析: {result.get('parse', 0)}")
        print(f"  播放地址: {result.get('playUrl', '')}")
        print(f"  原始URL: {result.get('url', '')}")
        
        # 验证播放地址是否有效
        play_addr = result.get('playUrl', '')
        if play_addr and play_addr.startswith('http'):
            if '.m3u8' in play_addr:
                print(f"  ✅ 成功解析m3u8播放地址")
            elif '.mp4' in play_addr:
                print(f"  ✅ 成功解析mp4播放地址")
            else:
                print(f"  ⚠️ 播放地址格式未知")
        else:
            print(f"  ❌ 播放地址解析失败")

def test_image_fix():
    """测试图片URL修复"""
    print("\n" + "=" * 60)
    print("测试图片URL修复")
    print("=" * 60)
    
    spider = Spider()
    spider.init()
    
    # 获取一些视频的图片URL
    result = spider.categoryContent('1', '1', False, {})
    
    if result.get('list'):
        print("图片URL测试:")
        for i, video in enumerate(result['list'][:5]):
            pic_url = video.get('vod_pic', '')
            print(f"  {i+1}. {video.get('vod_name', '')}")
            print(f"     图片URL: {pic_url}")
            
            if pic_url:
                if pic_url.startswith('http'):
                    print(f"     ✅ 完整URL")
                elif pic_url == '/loading.png':
                    print(f"     ⚠️ 占位符图片")
                else:
                    print(f"     ⚠️ 相对路径")
            else:
                print(f"     ❌ 无图片URL")

def main():
    """主测试函数"""
    print("nivod.vip 插件修复验证测试")
    print("开始时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 执行所有修复测试
        test_search_fix()
        test_category_fix()
        test_detail_fix()
        test_player_fix()
        test_image_fix()
        
        print("\n" + "=" * 60)
        print("所有修复测试完成")
        print("结束时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    import time
    main()
