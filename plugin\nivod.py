# -*- coding: utf-8 -*-
# nivod.vip 爬虫插件
# 网站：https://www.nivod.vip/

import sys
sys.path.append('..')
from base.spider import Spider
import json
import re
import time
import requests
from pyquery import PyQuery as pq
from urllib.parse import urljoin, quote
import base64
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def error_handler(default_return=None):
    """错误处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.RequestException as e:
                logging.error(f"{func.__name__} 网络请求错误: {str(e)}")
                return default_return
            except Exception as e:
                logging.error(f"{func.__name__} 执行错误: {str(e)}")
                return default_return
        return wrapper
    return decorator

class Spider(Spider):
    
    def init(self, extend=""):
        """初始化插件配置"""
        self.host = "https://www.nivod.vip"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.timeout = 10
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 分类映射
        self.type_map = {
            '1': '电影',
            '2': '剧集', 
            '3': '综艺',
            '4': '动漫'
        }
        
    def getName(self):
        """获取插件名称"""
        return "泥视频"
    
    def isVideoFormat(self, url):
        """判断是否为视频格式"""
        video_formats = ['.mp4', '.m3u8', '.flv', '.avi', '.mkv', '.mov', '.wmv']
        return any(fmt in url.lower() for fmt in video_formats)
    
    def manualVideoCheck(self):
        """手动视频检查"""
        return False
    
    def destroy(self):
        """销毁资源"""
        if hasattr(self, 'session'):
            self.session.close()

    def safe_request(self, url, method='GET', **kwargs):
        """安全的网络请求方法"""
        try:
            kwargs.setdefault('timeout', self.timeout)
            kwargs.setdefault('headers', self.headers)

            if method.upper() == 'GET':
                response = self.session.get(url, **kwargs)
            elif method.upper() == 'POST':
                response = self.session.post(url, **kwargs)
            else:
                raise ValueError(f"不支持的请求方法: {method}")

            response.raise_for_status()
            return response

        except requests.exceptions.Timeout:
            logging.error(f"请求超时: {url}")
            raise
        except requests.exceptions.ConnectionError:
            logging.error(f"连接错误: {url}")
            raise
        except requests.exceptions.HTTPError as e:
            logging.error(f"HTTP错误 {e.response.status_code}: {url}")
            raise
        except Exception as e:
            logging.error(f"请求异常: {url}, 错误: {str(e)}")
            raise
    
    def homeContent(self, filter):
        """
        获取首页内容，返回分类列表
        """
        try:
            result = {
                'class': [],
                'filters': {}
            }
            
            # 构建分类列表
            for type_id, type_name in self.type_map.items():
                result['class'].append({
                    'type_id': type_id,
                    'type_name': type_name
                })
            
            # 添加筛选条件（如果需要的话）
            result['filters'] = {
                '1': [  # 电影筛选
                    {
                        'key': 'class',
                        'name': '类型',
                        'value': [
                            {'n': '全部', 'v': ''},
                            {'n': '动作', 'v': '动作'},
                            {'n': '喜剧', 'v': '喜剧'},
                            {'n': '爱情', 'v': '爱情'},
                            {'n': '科幻', 'v': '科幻'},
                            {'n': '恐怖', 'v': '恐怖'},
                            {'n': '剧情', 'v': '剧情'},
                            {'n': '战争', 'v': '战争'}
                        ]
                    }
                ],
                '2': [  # 剧集筛选
                    {
                        'key': 'class', 
                        'name': '类型',
                        'value': [
                            {'n': '全部', 'v': ''},
                            {'n': '国产剧', 'v': '国产剧'},
                            {'n': '港台剧', 'v': '港台剧'},
                            {'n': '日韩剧', 'v': '日韩剧'},
                            {'n': '欧美剧', 'v': '欧美剧'}
                        ]
                    }
                ]
            }
            
            return result
            
        except Exception as e:
            print(f"homeContent error: {str(e)}")
            return {'class': [], 'filters': {}}
    
    def categoryContent(self, tid, pg, filter, extend):
        """
        获取分类页面内容
        tid: 分类ID
        pg: 页码
        filter: 筛选条件
        extend: 扩展参数
        """
        try:
            result = {
                'list': [],
                'page': int(pg),
                'pagecount': 1,
                'limit': 20,
                'total': 0
            }
            
            # 构建分类页面URL
            url = f"{self.host}/t/{tid}/"
            if int(pg) > 1:
                url += f"page/{pg}/"
            
            # 发送请求
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析页面
            doc = pq(response.text)

            # 提取视频列表 - 寻找包含/nivod/链接的元素
            video_links = doc('a[href*="/nivod/"]').items()

            for link in video_links:
                try:
                    detail_url = link.attr('href')
                    if not detail_url or '/nivod/' not in detail_url:
                        continue

                    # 提取视频ID
                    vod_id = re.search(r'/nivod/(\d+)/', detail_url)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)

                    # 提取标题 - 可能在链接文本或title属性中
                    title = link.attr('title') or link.text().strip()
                    if not title:
                        continue

                    # 提取封面图片 - 查找链接内的img标签
                    pic_elem = link.find('img')
                    pic = ''
                    if pic_elem:
                        pic = pic_elem.attr('data-src') or pic_elem.attr('src') or ''
                        if pic and pic != '/loading.png' and not pic.startswith('http'):
                            pic = urljoin(self.host, pic)

                    # 提取更新信息 - 查找相关的更新标识
                    remarks = ''
                    parent = link.parent()
                    if parent:
                        remarks_elem = parent.find('.module-item-note, .video-serial, .pic-text')
                        if remarks_elem:
                            remarks = remarks_elem.text().strip()

                    # 避免重复添加相同的视频
                    if any(item['vod_id'] == vod_id for item in result['list']):
                        continue

                    video_info = {
                        'vod_id': vod_id,
                        'vod_name': title,
                        'vod_pic': pic,
                        'vod_remarks': remarks
                    }

                    result['list'].append(video_info)

                except Exception as e:
                    print(f"Parse video item error: {str(e)}")
                    continue
            
            # 提取分页信息
            page_info = doc('.page-info').text()
            if page_info:
                page_match = re.search(r'(\d+)/(\d+)', page_info)
                if page_match:
                    result['page'] = int(page_match.group(1))
                    result['pagecount'] = int(page_match.group(2))
            
            result['total'] = len(result['list'])
            
            return result
            
        except Exception as e:
            print(f"categoryContent error: {str(e)}")
            return {'list': [], 'page': int(pg), 'pagecount': 1, 'limit': 20, 'total': 0}
    
    def detailContent(self, ids):
        """
        获取视频详情
        ids: 视频ID列表
        """
        try:
            result = {'list': []}
            
            for vod_id in ids:
                # 构建详情页URL
                url = f"{self.host}/nivod/{vod_id}/"
                
                # 发送请求
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                # 解析页面
                doc = pq(response.text)

                # 提取基本信息
                title = doc('h1').text().strip()
                if not title:
                    title = doc('.video-info-title, .module-info-title').text().strip()

                # 提取封面图片
                pic_elem = doc('img').eq(0)
                pic = ''
                if pic_elem:
                    pic = pic_elem.attr('data-src') or pic_elem.attr('src') or ''
                    if pic and pic != '/loading.png' and not pic.startswith('http'):
                        pic = urljoin(self.host, pic)

                # 提取详细信息 - 从页面文本中解析
                page_text = doc.text()
                year = ''
                area = ''
                category = ''
                director = ''
                actor = ''
                content = ''

                # 从链接和文本中提取信息
                year_match = re.search(r'(\d{4})', page_text)
                if year_match:
                    year = year_match.group(1)

                # 从分类链接中提取地区和类型
                area_links = doc('a[href*="地区"], a[href*="大陆"], a[href*="美国"], a[href*="韩国"], a[href*="日本"]')
                if area_links:
                    area = area_links.eq(0).text().strip()

                category_links = doc('a[href*="类型"], a[href*="动作"], a[href*="喜剧"], a[href*="爱情"]')
                if category_links:
                    category = category_links.eq(0).text().strip()

                # 从页面中提取导演和主演信息
                director_match = re.search(r'导演[：:]\s*([^/\n]+)', page_text)
                if director_match:
                    director = director_match.group(1).strip()

                actor_match = re.search(r'主演[：:]\s*([^/\n]+)', page_text)
                if actor_match:
                    actor = actor_match.group(1).strip()

                # 提取剧情简介
                content_elem = doc('p').filter(lambda i, e: len(pq(e).text()) > 50)
                if content_elem:
                    content = content_elem.eq(0).text().strip()
                
                # 提取播放列表
                play_list = {}

                # 查找播放源标签
                source_tabs = doc('a[href*="/niplay/"]').items()
                current_source = "默认播放源"
                episodes = []

                for tab in source_tabs:
                    episode_title = tab.text().strip()
                    episode_url = tab.attr('href')

                    if episode_url and episode_title:
                        episodes.append(f"{episode_title}${episode_url}")

                if episodes:
                    play_list[current_source] = '#'.join(episodes)

                # 如果没有找到播放列表，尝试其他方法
                if not play_list:
                    # 查找所有包含niplay的链接
                    play_links = doc('a[href*="niplay"]').items()
                    episodes = []

                    for link in play_links:
                        episode_title = link.text().strip()
                        episode_url = link.attr('href')

                        if episode_url and episode_title:
                            episodes.append(f"{episode_title}${episode_url}")

                    if episodes:
                        play_list["播放列表"] = '#'.join(episodes)
                
                video_info = {
                    'vod_id': vod_id,
                    'vod_name': title,
                    'vod_pic': pic,
                    'type_name': category,
                    'vod_year': year,
                    'vod_area': area,
                    'vod_director': director,
                    'vod_actor': actor,
                    'vod_content': content,
                    'vod_play_from': '$$$'.join(play_list.keys()),
                    'vod_play_url': '$$$'.join(play_list.values())
                }
                
                result['list'].append(video_info)
            
            return result
            
        except Exception as e:
            print(f"detailContent error: {str(e)}")
            return {'list': []}
    
    def searchContent(self, key, quick, pg="1"):
        """
        搜索内容
        key: 搜索关键词
        quick: 是否快速搜索
        pg: 页码
        """
        try:
            result = {
                'list': [],
                'page': int(pg),
                'pagecount': 1,
                'limit': 20,
                'total': 0
            }
            
            # 构建搜索URL
            encoded_key = quote(key)
            url = f"{self.host}/s/{encoded_key}-------------/"
            if int(pg) > 1:
                url += f"page/{pg}/"
            
            # 发送请求
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析搜索结果
            doc = pq(response.text)

            # 提取搜索结果 - 寻找包含/nivod/链接的元素
            search_links = doc('a[href*="/nivod/"]').items()

            for link in search_links:
                try:
                    detail_url = link.attr('href')
                    if not detail_url or '/nivod/' not in detail_url:
                        continue

                    # 提取视频ID
                    vod_id = re.search(r'/nivod/(\d+)/', detail_url)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)

                    # 提取标题
                    title = link.attr('title') or link.text().strip()
                    if not title:
                        continue

                    # 提取封面图片
                    pic_elem = link.find('img')
                    pic = ''
                    if pic_elem:
                        pic = pic_elem.attr('data-src') or pic_elem.attr('src') or ''
                        if pic and pic != '/loading.png' and not pic.startswith('http'):
                            pic = urljoin(self.host, pic)

                    # 提取更新信息
                    remarks = ''
                    parent = link.parent()
                    if parent:
                        remarks_elem = parent.find('.video-serial, .pic-text')
                        if remarks_elem:
                            remarks = remarks_elem.text().strip()

                    # 避免重复添加相同的视频
                    if any(item['vod_id'] == vod_id for item in result['list']):
                        continue

                    video_info = {
                        'vod_id': vod_id,
                        'vod_name': title,
                        'vod_pic': pic,
                        'vod_remarks': remarks
                    }

                    result['list'].append(video_info)

                except Exception as e:
                    print(f"Parse search item error: {str(e)}")
                    continue
            
            result['total'] = len(result['list'])
            
            return result
            
        except Exception as e:
            print(f"searchContent error: {str(e)}")
            return {'list': [], 'page': int(pg), 'pagecount': 1, 'limit': 20, 'total': 0}
    
    def playerContent(self, flag, id, vipFlags):
        """
        获取播放地址
        flag: 播放源标识
        id: 播放地址ID
        vipFlags: VIP标识
        """
        try:
            result = {
                'parse': 0,
                'playUrl': '',
                'url': id
            }

            # 如果id就是播放页面URL，直接访问
            if id.startswith('/niplay/'):
                play_url = urljoin(self.host, id)
            else:
                play_url = id

            # 发送请求获取播放页面
            response = self.session.get(play_url, timeout=self.timeout)
            response.raise_for_status()

            # 解析播放页面，寻找真实播放地址
            doc = pq(response.text)

            # 方法1: 查找页面中的视频标签
            video_elem = doc('video source, video')
            if video_elem:
                video_src = video_elem.attr('src')
                if video_src:
                    result['playUrl'] = video_src
                    result['url'] = video_src
                    return result

            # 方法2: 查找JavaScript中的播放地址
            script_text = response.text

            # 查找常见的视频URL模式
            video_patterns = [
                r'"url"\s*:\s*"([^"]+\.m3u8[^"]*)"',
                r'"url"\s*:\s*"([^"]+\.mp4[^"]*)"',
                r'playUrl\s*=\s*["\']([^"\']+)["\']',
                r'video_url\s*=\s*["\']([^"\']+)["\']',
                r'src\s*=\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
                r'src\s*=\s*["\']([^"\']+\.mp4[^"\']*)["\']'
            ]

            for pattern in video_patterns:
                match = re.search(pattern, script_text, re.IGNORECASE)
                if match:
                    video_url = match.group(1)
                    # 处理相对URL
                    if not video_url.startswith('http'):
                        video_url = urljoin(self.host, video_url)
                    result['playUrl'] = video_url
                    result['url'] = video_url
                    return result

            # 方法3: 如果没有找到直接播放地址，返回播放页面URL
            # 这种情况下，播放器可能需要进一步解析
            result['parse'] = 1  # 需要解析
            result['playUrl'] = play_url
            result['url'] = play_url

            return result

        except Exception as e:
            print(f"playerContent error: {str(e)}")
            return {'parse': 0, 'playUrl': '', 'url': id}

# 调试代码
if __name__ == "__main__":
    spider = Spider()
    spider.init()

    print("=== 测试首页内容 ===")
    home_result = spider.homeContent(False)
    print(json.dumps(home_result, ensure_ascii=False, indent=2))

    print("\n=== 测试分类内容 ===")
    category_result = spider.categoryContent('1', '1', False, {})
    print(f"找到 {len(category_result['list'])} 个视频")
    if category_result['list']:
        print("第一个视频:", json.dumps(category_result['list'][0], ensure_ascii=False, indent=2))

    print("\n=== 测试搜索功能 ===")
    search_result = spider.searchContent('利剑', False, '1')
    print(f"搜索到 {len(search_result['list'])} 个结果")
    if search_result['list']:
        print("第一个结果:", json.dumps(search_result['list'][0], ensure_ascii=False, indent=2))

    print("\n=== 测试详情内容 ===")
    if category_result['list']:
        test_id = category_result['list'][0]['vod_id']
        detail_result = spider.detailContent([test_id])
        print(f"详情测试ID: {test_id}")
        if detail_result['list']:
            detail = detail_result['list'][0]
            print("视频详情:")
            print(f"  标题: {detail.get('vod_name', '')}")
            print(f"  年份: {detail.get('vod_year', '')}")
            print(f"  地区: {detail.get('vod_area', '')}")
            print(f"  类型: {detail.get('type_name', '')}")
            print(f"  导演: {detail.get('vod_director', '')}")
            print(f"  主演: {detail.get('vod_actor', '')}")
            print(f"  播放源: {detail.get('vod_play_from', '')}")
            print(f"  剧情: {detail.get('vod_content', '')[:100]}...")

    print("\n=== 测试播放地址 ===")
    if search_result['list']:
        test_id = search_result['list'][0]['vod_id']
        detail_result = spider.detailContent([test_id])
        if detail_result['list'] and detail_result['list'][0].get('vod_play_url'):
            play_urls = detail_result['list'][0]['vod_play_url'].split('$$$')[0]
            if play_urls:
                first_episode = play_urls.split('#')[0]
                if '$' in first_episode:
                    episode_url = first_episode.split('$')[1]
                    player_result = spider.playerContent('', episode_url, '')
                    print(f"播放地址测试: {player_result.get('playUrl', '')}")
