# -*- coding: utf-8 -*-
# nivod.vip 插件调试脚本
# 用于全面测试插件的各项功能

import sys
sys.path.append('..')
import json
import time
from nivod import Spider

def test_home_content():
    """测试首页内容"""
    print("=" * 50)
    print("测试首页内容 (homeContent)")
    print("=" * 50)
    
    spider = Spider()
    spider.init()
    
    result = spider.homeContent(False)
    
    print(f"分类数量: {len(result.get('class', []))}")
    print("分类列表:")
    for category in result.get('class', []):
        print(f"  - {category.get('type_name')} (ID: {category.get('type_id')})")
    
    print(f"筛选条件数量: {len(result.get('filters', {}))}")
    
    return result

def test_category_content():
    """测试分类内容"""
    print("\n" + "=" * 50)
    print("测试分类内容 (categoryContent)")
    print("=" * 50)
    
    spider = Spider()
    spider.init()
    
    # 测试电影分类
    print("测试电影分类 (type_id=1):")
    result = spider.categoryContent('1', '1', False, {})
    
    print(f"找到视频数量: {len(result.get('list', []))}")
    print(f"当前页码: {result.get('page', 0)}")
    print(f"总页数: {result.get('pagecount', 0)}")
    
    if result.get('list'):
        print("\n前3个视频:")
        for i, video in enumerate(result['list'][:3]):
            print(f"  {i+1}. {video.get('vod_name', '')} (ID: {video.get('vod_id', '')})")
            print(f"     备注: {video.get('vod_remarks', '')}")
    
    return result

def test_search_content():
    """测试搜索功能"""
    print("\n" + "=" * 50)
    print("测试搜索功能 (searchContent)")
    print("=" * 50)
    
    spider = Spider()
    spider.init()
    
    # 测试搜索关键词
    keywords = ['利剑', '凡人', '完美世界']
    
    for keyword in keywords:
        print(f"\n搜索关键词: '{keyword}'")
        result = spider.searchContent(keyword, False, '1')
        
        print(f"搜索结果数量: {len(result.get('list', []))}")
        
        if result.get('list'):
            print("搜索结果:")
            for i, video in enumerate(result['list'][:2]):
                print(f"  {i+1}. {video.get('vod_name', '')} (ID: {video.get('vod_id', '')})")
    
    return result

def test_detail_content():
    """测试详情内容"""
    print("\n" + "=" * 50)
    print("测试详情内容 (detailContent)")
    print("=" * 50)
    
    spider = Spider()
    spider.init()
    
    # 先获取一些视频ID
    category_result = spider.categoryContent('1', '1', False, {})
    
    if not category_result.get('list'):
        print("无法获取测试视频ID")
        return None
    
    # 测试前2个视频的详情
    test_ids = [video['vod_id'] for video in category_result['list'][:2]]
    
    for vod_id in test_ids:
        print(f"\n测试视频ID: {vod_id}")
        result = spider.detailContent([vod_id])
        
        if result.get('list'):
            detail = result['list'][0]
            print(f"  标题: {detail.get('vod_name', '')}")
            print(f"  年份: {detail.get('vod_year', '')}")
            print(f"  地区: {detail.get('vod_area', '')}")
            print(f"  类型: {detail.get('type_name', '')}")
            print(f"  导演: {detail.get('vod_director', '')}")
            print(f"  主演: {detail.get('vod_actor', '')}")
            
            # 播放源信息
            play_from = detail.get('vod_play_from', '')
            play_url = detail.get('vod_play_url', '')
            
            if play_from:
                sources = play_from.split('$$$')
                print(f"  播放源数量: {len(sources)}")
                print(f"  播放源: {', '.join(sources)}")
            
            if play_url:
                urls = play_url.split('$$$')
                for i, url_list in enumerate(urls):
                    episodes = url_list.split('#')
                    print(f"  播放源{i+1}集数: {len(episodes)}")
            
            # 剧情简介
            content = detail.get('vod_content', '')
            if content:
                print(f"  剧情: {content[:100]}...")
        else:
            print(f"  获取详情失败")
    
    return result

def test_player_content():
    """测试播放地址"""
    print("\n" + "=" * 50)
    print("测试播放地址 (playerContent)")
    print("=" * 50)
    
    spider = Spider()
    spider.init()
    
    # 先获取一个视频的播放地址
    search_result = spider.searchContent('利剑', False, '1')
    
    if not search_result.get('list'):
        print("无法获取测试播放地址")
        return None
    
    test_id = search_result['list'][0]['vod_id']
    detail_result = spider.detailContent([test_id])
    
    if not detail_result.get('list') or not detail_result['list'][0].get('vod_play_url'):
        print("无法获取播放列表")
        return None
    
    # 获取第一个播放地址
    play_urls = detail_result['list'][0]['vod_play_url'].split('$$$')[0]
    if not play_urls:
        print("播放列表为空")
        return None
    
    first_episode = play_urls.split('#')[0]
    if '$' not in first_episode:
        print("播放地址格式错误")
        return None
    
    episode_title, episode_url = first_episode.split('$', 1)
    
    print(f"测试播放地址: {episode_title}")
    print(f"播放URL: {episode_url}")
    
    result = spider.playerContent('', episode_url, '')
    
    print(f"解析结果:")
    print(f"  需要解析: {result.get('parse', 0)}")
    print(f"  播放地址: {result.get('playUrl', '')}")
    print(f"  原始URL: {result.get('url', '')}")
    
    return result

def test_performance():
    """性能测试"""
    print("\n" + "=" * 50)
    print("性能测试")
    print("=" * 50)
    
    spider = Spider()
    spider.init()
    
    # 测试各个方法的响应时间
    tests = [
        ('首页内容', lambda: spider.homeContent(False)),
        ('分类内容', lambda: spider.categoryContent('1', '1', False, {})),
        ('搜索功能', lambda: spider.searchContent('测试', False, '1')),
    ]
    
    for test_name, test_func in tests:
        start_time = time.time()
        try:
            result = test_func()
            end_time = time.time()
            duration = end_time - start_time
            
            success = "成功" if result else "失败"
            print(f"{test_name}: {success} - 耗时 {duration:.2f}秒")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"{test_name}: 异常 - 耗时 {duration:.2f}秒 - 错误: {str(e)}")

def main():
    """主测试函数"""
    print("nivod.vip 插件全面测试")
    print("开始时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 执行所有测试
        test_home_content()
        test_category_content()
        test_search_content()
        test_detail_content()
        test_player_content()
        test_performance()
        
        print("\n" + "=" * 50)
        print("所有测试完成")
        print("结束时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
