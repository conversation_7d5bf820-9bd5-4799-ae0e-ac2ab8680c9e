# Pyramid爬虫项目虚拟环境配置说明

## 1. 虚拟环境创建

### 1.1 创建虚拟环境
```bash
# 在项目根目录下执行
python -m venv pyramid_env
```

### 1.2 激活虚拟环境
```bash
# Windows系统
pyramid_env\Scripts\activate

# Linux/Mac系统
source pyramid_env/bin/activate
```

### 1.3 升级pip
```bash
python -m pip install --upgrade pip
```

## 2. 依赖包安装

### 2.1 核心依赖包
```bash
pip install requests pyquery lxml pycryptodome aiohttp
```

### 2.2 已安装的完整依赖列表
- **requests** (2.32.4) - HTTP请求库
- **pyquery** (2.0.1) - jQuery风格的HTML解析库
- **lxml** (6.0.0) - XML和HTML解析库
- **pycryptodome** (3.23.0) - 加密解密库
- **aiohttp** (3.12.15) - 异步HTTP客户端/服务器

### 2.3 自动安装的依赖包
- aiohappyeyeballs (2.6.1)
- aiosignal (1.4.0)
- attrs (25.3.0)
- certifi (2025.7.14)
- charset_normalizer (3.4.2)
- cssselect (1.3.0)
- frozenlist (1.7.0)
- idna (3.10)
- multidict (6.6.3)
- propcache (0.3.2)
- typing-extensions (4.14.1)
- urllib3 (2.5.0)
- yarl (1.20.1)

## 3. 环境验证

### 3.1 测试调试示例
```bash
# 激活虚拟环境
pyramid_env\Scripts\activate

# 进入plugin目录
cd plugin

# 运行调试示例
python 小白调试示例.py
```

### 3.2 预期输出
```json
{'class': [{'type_name': '穿越', 'type_id': '穿越'}]}
```

## 4. 开发环境配置

### 4.1 IDE配置
1. **VSCode配置**
   - 选择Python解释器：`pyramid_env\Scripts\python.exe`
   - 设置工作目录为项目根目录

2. **PyCharm配置**
   - 添加Python解释器：指向`pyramid_env\Scripts\python.exe`
   - 设置项目根目录为源码根目录

### 4.2 调试配置
```python
# 在plugin目录下新建插件文件时，使用以下模板
import sys
sys.path.append('..')  # 添加父目录到Python路径
from base.spider import Spider

# 调试代码模板
if __name__ == "__main__":
    sp = Spider()
    sp.init([])
    result = sp.homeContent(False)
    print(result)
```

## 5. 常用开发命令

### 5.1 环境管理
```bash
# 激活环境
pyramid_env\Scripts\activate

# 退出环境
deactivate

# 查看已安装包
pip list

# 导出依赖列表
pip freeze > requirements.txt
```

### 5.2 插件开发流程
```bash
# 1. 激活虚拟环境
pyramid_env\Scripts\activate

# 2. 进入plugin目录
cd plugin

# 3. 创建新插件文件
# 新建 xxx.py 文件

# 4. 运行测试
python xxx.py

# 5. 调试和完善代码
# 根据输出结果调试代码
```

## 6. 项目结构说明

```
PyramidStore-18-main/
├── pyramid_env/          # 虚拟环境目录
├── base/                 # 基础模块
│   ├── spider.py        # Spider基类
│   └── localProxy.py    # 代理配置
├── plugin/              # 插件目录
│   ├── adult/          # 成人内容插件
│   ├── app/            # APP类插件
│   ├── html/           # 网页类插件
│   ├── official/       # 官方平台插件
│   ├── tools/          # 工具脚本
│   └── 小白调试示例.py  # 调试示例
├── example.json         # 示例配置
├── spider.md           # 说明文档
└── README.md           # 项目说明
```

## 7. 注意事项

### 7.1 路径问题
- 所有插件都需要使用 `sys.path.append('..')` 来导入base模块
- 运行插件时需要在plugin目录下执行
- 虚拟环境路径为相对路径，不要移动项目目录

### 7.2 依赖管理
- 严格按照依赖分析报告中的模块列表开发
- 不得随意安装新的第三方库
- 如需新依赖，必须先进行评估

### 7.3 开发规范
- 遵循插件开发规范文档
- 使用统一的代码风格和命名规范
- 完善异常处理和错误日志

## 8. 故障排除

### 8.1 常见问题
1. **ModuleNotFoundError: No module named 'base'**
   - 解决：确保在plugin目录下运行，并添加了`sys.path.append('..')`

2. **ImportError: No module named 'requests'**
   - 解决：确保激活了虚拟环境并安装了依赖

3. **编码错误**
   - 解决：确保文件头部有`# -*- coding: utf-8 -*-`

### 8.2 重新安装环境
```bash
# 删除虚拟环境
rmdir /s pyramid_env

# 重新创建
python -m venv pyramid_env
pyramid_env\Scripts\activate
pip install --upgrade pip
pip install requests pyquery lxml pycryptodome aiohttp
```

## 9. 开发建议

### 9.1 最佳实践
- 每次开发前先激活虚拟环境
- 使用调试示例作为开发模板
- 频繁测试，及时发现问题
- 保持代码简洁和可读性

### 9.2 性能优化
- 合理使用缓存机制
- 适当使用并发处理
- 避免重复请求
- 优化正则表达式

## 10. 后续维护

### 10.1 依赖更新
```bash
# 查看可更新的包
pip list --outdated

# 更新特定包
pip install --upgrade package_name

# 更新所有包（谨慎使用）
pip install --upgrade -r requirements.txt
```

### 10.2 环境备份
```bash
# 导出当前环境
pip freeze > requirements.txt

# 在新环境中恢复
pip install -r requirements.txt
```

---

**配置完成！** 虚拟环境已成功创建并配置，所有依赖包已安装完毕，调试示例运行正常。现在可以开始进行插件开发工作了。
