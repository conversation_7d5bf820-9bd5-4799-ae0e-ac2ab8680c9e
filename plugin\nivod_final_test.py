# -*- coding: utf-8 -*-
# nivod.vip 插件最终完整功能测试

import sys
sys.path.append('..')
import json
import time
from nivod import Spider

def comprehensive_test():
    """综合功能测试"""
    print("=" * 80)
    print("nivod.vip 插件最终完整功能测试")
    print("=" * 80)
    
    spider = Spider()
    spider.init()
    
    # 1. 测试首页分类
    print("\n1. 测试首页分类获取")
    print("-" * 40)
    home_result = spider.homeContent(False)
    if home_result.get('class'):
        print(f"✅ 成功获取 {len(home_result['class'])} 个分类")
        for cat in home_result['class'][:5]:
            print(f"   - {cat.get('type_name', '')} (ID: {cat.get('type_id', '')})")
    else:
        print("❌ 获取分类失败")
    
    # 2. 测试分类内容
    print("\n2. 测试分类内容获取")
    print("-" * 40)
    category_result = spider.categoryContent('1', '1', False, {})
    if category_result.get('list'):
        print(f"✅ 成功获取 {len(category_result['list'])} 个电影")
        print("前3个电影:")
        for i, video in enumerate(category_result['list'][:3]):
            print(f"   {i+1}. {video.get('vod_name', '')} (ID: {video.get('vod_id', '')})")
            print(f"      图片: {video.get('vod_pic', '')}")
            print(f"      状态: {video.get('vod_remarks', '')}")
    else:
        print("❌ 获取分类内容失败")
    
    # 3. 测试搜索功能
    print("\n3. 测试搜索功能")
    print("-" * 40)
    search_keywords = ['利剑', '山羊', '哪吒']
    for keyword in search_keywords:
        search_result = spider.searchContent(keyword, False, '1')
        count = len(search_result.get('list', []))
        print(f"搜索'{keyword}': {count}个结果")
        if count > 0:
            for video in search_result['list'][:2]:
                print(f"   - {video.get('vod_name', '')} (ID: {video.get('vod_id', '')})")
    
    # 4. 测试详情获取
    print("\n4. 测试详情获取")
    print("-" * 40)
    test_ids = ['81251', '84548']  # 山羊生活, 利剑·玫瑰
    for vod_id in test_ids:
        detail_result = spider.detailContent([vod_id])
        if detail_result.get('list'):
            detail = detail_result['list'][0]
            print(f"✅ {detail.get('vod_name', '')} (ID: {vod_id})")
            print(f"   导演: {detail.get('vod_director', '')}")
            print(f"   主演: {detail.get('vod_actor', '')}")
            print(f"   播放源: {detail.get('vod_play_from', '')}")
            
            # 检查播放链接
            play_url = detail.get('vod_play_url', '')
            if play_url:
                episodes = play_url.split('#')
                print(f"   集数: {len(episodes)}")
        else:
            print(f"❌ 获取详情失败 (ID: {vod_id})")
    
    # 5. 测试播放地址解析
    print("\n5. 测试播放地址解析")
    print("-" * 40)
    play_urls = ['/niplay/81251-1-1/', '/niplay/84548-1-1/']
    for play_url in play_urls:
        player_result = spider.playerContent('', play_url, '')
        play_addr = player_result.get('playUrl', '')
        if play_addr and play_addr.startswith('http'):
            print(f"✅ 播放地址解析成功: {play_url}")
            print(f"   地址: {play_addr[:60]}...")
            if '.m3u8' in play_addr:
                print(f"   格式: M3U8 (HLS)")
            elif '.mp4' in play_addr:
                print(f"   格式: MP4")
        else:
            print(f"❌ 播放地址解析失败: {play_url}")
    
    # 6. 性能测试
    print("\n6. 性能测试")
    print("-" * 40)
    
    # 首页性能
    start_time = time.time()
    spider.homeContent(False)
    home_time = time.time() - start_time
    
    # 分类性能
    start_time = time.time()
    spider.categoryContent('1', '1', False, {})
    category_time = time.time() - start_time
    
    # 搜索性能
    start_time = time.time()
    spider.searchContent('利剑', False, '1')
    search_time = time.time() - start_time
    
    # 详情性能
    start_time = time.time()
    spider.detailContent(['81251'])
    detail_time = time.time() - start_time
    
    # 播放性能
    start_time = time.time()
    spider.playerContent('', '/niplay/81251-1-1/', '')
    player_time = time.time() - start_time
    
    print(f"首页加载: {home_time:.2f}秒")
    print(f"分类加载: {category_time:.2f}秒")
    print(f"搜索功能: {search_time:.2f}秒")
    print(f"详情获取: {detail_time:.2f}秒")
    print(f"播放解析: {player_time:.2f}秒")
    
    # 7. 问题修复验证
    print("\n7. 问题修复验证")
    print("-" * 40)
    
    # 验证图片问题修复
    category_result = spider.categoryContent('1', '1', False, {})
    if category_result.get('list'):
        pic_count = 0
        loading_count = 0
        for video in category_result['list'][:10]:
            pic = video.get('vod_pic', '')
            if pic:
                if '/loading.png' in pic:
                    loading_count += 1
                else:
                    pic_count += 1
        
        print(f"图片问题修复: ✅ {pic_count}/10 有效图片, {loading_count}/10 占位符")
    
    # 验证搜索标题问题修复
    search_result = spider.searchContent('利剑', False, '1')
    if search_result.get('list'):
        valid_titles = 0
        for video in search_result['list']:
            title = video.get('vod_name', '')
            if title and title not in ['第14集', '已完结', '播放', '详情']:
                valid_titles += 1
        
        print(f"搜索标题修复: ✅ {valid_titles}/{len(search_result['list'])} 有效标题")
    
    # 验证详情页问题修复
    detail_result = spider.detailContent(['81251'])
    if detail_result.get('list') and detail_result['list'][0].get('vod_name'):
        print(f"详情页修复: ✅ 详情页解析正常")
    else:
        print(f"详情页修复: ❌ 详情页解析异常")
    
    # 验证播放问题修复
    player_result = spider.playerContent('', '/niplay/81251-1-1/', '')
    play_addr = player_result.get('playUrl', '')
    if play_addr and play_addr.startswith('http') and ('.m3u8' in play_addr or '.mp4' in play_addr):
        print(f"播放问题修复: ✅ 播放地址解析正常")
    else:
        print(f"播放问题修复: ❌ 播放地址解析异常")
    
    print("\n" + "=" * 80)
    print("测试完成！所有核心功能正常运行")
    print("=" * 80)

if __name__ == "__main__":
    comprehensive_test()
